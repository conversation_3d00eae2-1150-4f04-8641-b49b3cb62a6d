// ==========================================
// GAME MANAGER
// ==========================================
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class GameManager : MonoBehaviour
{
    [Header("UI References")]
    public Text scoreText;
    public Text sizeText;
    public Slider sizeSlider;
    public GameObject gameOverPanel;
    public Text finalScoreText;

    [Header("Game Settings")]
    public int targetScore = 1000;
    public float levelCompleteDelay = 3f;

    // Game state
    private int currentScore = 0;
    private bool gameEnded = false;
    private TornadoController tornado;

    void Start()
    {
        tornado = FindObjectOfType<TornadoController>();

        // Initialize UI
        if (gameOverPanel != null)
            gameOverPanel.SetActive(false);

        UpdateUI();
    }

    void Update()
    {
        UpdateUI();
        CheckWinCondition();
    }

    public void AddScore(int points)
    {
        if (!gameEnded)
        {
            currentScore += points;
        }
    }

    void UpdateUI()
    {
        if (tornado == null) return;

        // Update score
        if (scoreText != null)
            scoreText.text = "Score: " + currentScore;

        // Update size display
        if (sizeText != null)
            sizeText.text = "Size: " + tornado.currentSize.ToString("F1");

        // Update size slider
        if (sizeSlider != null)
        {
            sizeSlider.value = tornado.currentSize / tornado.maxSize;
        }
    }

    void CheckWinCondition()
    {
        if (!gameEnded && currentScore >= targetScore)
        {
            LevelComplete();
        }
    }

    void LevelComplete()
    {
        gameEnded = true;
        Debug.Log("Level Complete! Score: " + currentScore);

        // Could trigger next level or victory screen
        Invoke("RestartLevel", levelCompleteDelay);
    }

    public void GameOver()
    {
        if (gameEnded) return;

        gameEnded = true;

        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(true);
        }

        if (finalScoreText != null)
        {
            finalScoreText.text = "Final Score: " + currentScore;
        }

        // Freeze tornado
        if (tornado != null)
        {
            tornado.enabled = false;
        }
    }

    public void RestartLevel()
    {
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    public void QuitGame()
    {
        Application.Quit();
    }
}