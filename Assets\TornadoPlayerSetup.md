# 🌪️ GERÇEK TORNADO SİSTEMİ

## Özellikler

Tornado artık GERÇEK tornado gibi çalışıyor:

- ✅ HER ŞEYİ yakalayıp havaya kaldırır (destructible olmasına gerek yok!)
- ✅ Nesneleri etrafında döndürür
- ✅ 5 saniye sonra güçlü bir şekilde fırlatır
- ✅ Oyuncuyu da yakalayıp aynı şeyi yapar!

## <PERSON><PERSON>lum Adımları

### 1. Player Objesi Oluşturma

1. Hierarchy'de sağ tık → 3D Object → Capsule
2. Adını "Player" olarak değiştir
3. Tag'ini "Player" olarak ayarla
4. `SimplePlayerController` script'ini ekle
5. `Rigidbody` component'i ekle (otomatik eklenir)

### 2. Tornado Ayarları

TornadoController'da yeni ayarlar:

- **Player Suction Force**: 800 (Oyuncu çekme kuvveti)
- **Player Orbit Radius**: 3 (<PERSON><PERSON><PERSON>)
- **Player Orbit Height**: 4 (<PERSON>yun<PERSON>)
- **Player Tag**: "Player" (Oyuncu tag'i)

### 3. Test Nesneleri Oluşturma

1. Hierarchy'de sağ tık → 3D Object → Cube (birkaç tane)
2. Hierarchy'de sağ tık → 3D Object → Sphere (birkaç tane)
3. Nesneleri tornado etrafına yerleştir
4. İsteğe bağlı: DestructibleObject script'i ekle (daha fazla puan için)

### 4. Test Etme

1. Play butonuna bas
2. WASD ile tornado'yu hareket ettir
3. Nesnelere yaklaş - HER ŞEYİ yakalayacak!
4. Oyuncuya yaklaş - Onu da yakalayacak!
5. 5 saniye boyunca her şey etrafında dönecek
6. Sonra güçlü bir şekilde fırlatılacak!

## Tornado Etkisi Aşamaları

### 1. Yaklaşma (Approach)

- Oyuncu tornado'nun `suctionRange` alanına girdiğinde
- Tornado oyuncuyu çekmeye başlar

### 2. Yakalama (Capture)

- Oyuncu yakalandığında:
  - Gravity kapanır
  - Hava direnci artar
  - Orbit hareketi başlar

### 3. Dönme (Spinning)

- 5 saniye boyunca tornado etrafında döner
- Yukarı kaldırma kuvveti uygulanır
- Spin efekti eklenir

### 4. Fırlatma (Throwing)

- 5 saniye sonra güçlü fırlatma
- Rastgele yön (yukarı ağırlıklı)
- Gravity tekrar açılır

## Debug Mesajları

Console'da şu mesajları göreceksin:

- 🎮 Oyuncu bulundu: Player
- 🌪️ OYUNCU YAKALANDI! Tornado etkisinde!
- 💥 OYUNCU FIRLATLADI! Yön: (x,y,z) - Kuvvet: xxx

## Gizmo Görselleştirme

Scene view'da tornado seçiliyken:

- Sarı: Suction Range (Çekme alanı)
- Kırmızı: Tornado merkezi
- Mavi: Nesnelerin orbit yolu
- Magenta: Oyuncunun orbit yolu (yakalandığında)
- Yeşil: Oyuncunun mevcut pozisyonu

## İpuçları

- Tornado'dan kaçmak için hızlıca uzaklaş
- Yakalandıktan sonra kaçış yok - 5 saniye beklemen gerek!
- Fırlatıldıktan sonra yere düşerken dikkatli ol
- Büyük tornado daha güçlü çeker ve fırlatır
