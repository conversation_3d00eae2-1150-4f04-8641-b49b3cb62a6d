Using pre-set license
Built from '6000.0/release' branch; Version is '6000.0.46f1 (fb93bc360d3a) revision 16487356'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'tr' Physical Memory: 32373 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.46f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/My project (6)
-logFile
Logs/AssetImportWorker2.log
-srvPort
64579
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/My project (6)
C:/Users/<USER>/My project (6)
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [3864]  Target information:

Player connection [3864]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2875348985 [EditorId] 2875348985 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-S6BTMFP) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [3864] Host joined multi-casting on [***********:54997]...
Player connection [3864] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 42.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.46f1 (fb93bc360d3a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/My project (6)/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon RX 7800 XT (ID=0x747e)
    Vendor:   ATI
    VRAM:     16368 MB
    Driver:   32.0.21025.1024
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56932
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002906 seconds.
- Loaded All Assemblies, in  0.755 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.239 seconds
Domain Reload Profiling: 992ms
	BeginReloadAssembly (102ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (147ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (428ms)
		LoadAssemblies (99ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (425ms)
			TypeCache.Refresh (424ms)
				TypeCache.ScanAssembly (415ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (239ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (206ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.599 seconds
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.805 seconds
Domain Reload Profiling: 1402ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (423ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (224ms)
			TypeCache.Refresh (178ms)
				TypeCache.ScanAssembly (121ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (426ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (242ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5982 unused Assets / (6.2 MB). Loaded Objects now: 6624.
Memory consumption went from 155.7 MB to 149.6 MB.
Total: 6.378500 ms (FindLiveObjects: 0.548200 ms CreateObjectMapping: 0.244900 ms MarkObjects: 3.522000 ms  DeleteObjects: 2.062900 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.496 seconds
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.514 seconds
Domain Reload Profiling: 1011ms
	BeginReloadAssembly (167ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (278ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (124ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (107ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (515ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (364ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (78ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.3 MB). Loaded Objects now: 6640.
Memory consumption went from 144.5 MB to 138.2 MB.
Total: 7.230100 ms (FindLiveObjects: 0.539000 ms CreateObjectMapping: 0.477500 ms MarkObjects: 3.858100 ms  DeleteObjects: 2.354800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.517 seconds
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.520 seconds
Domain Reload Profiling: 1037ms
	BeginReloadAssembly (166ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (288ms)
		LoadAssemblies (237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (125ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (108ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (520ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (70ms)
			ProcessInitializeOnLoadAttributes (265ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.6 MB). Loaded Objects now: 6642.
Memory consumption went from 142.5 MB to 135.9 MB.
Total: 9.150700 ms (FindLiveObjects: 0.525700 ms CreateObjectMapping: 0.459400 ms MarkObjects: 4.841300 ms  DeleteObjects: 3.323400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.459 seconds
Refreshing native plugins compatible for Editor in 0.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.501 seconds
Domain Reload Profiling: 960ms
	BeginReloadAssembly (139ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (266ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (115ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (501ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (368ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (239ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.1 MB). Loaded Objects now: 6644.
Memory consumption went from 142.5 MB to 136.5 MB.
Total: 8.042300 ms (FindLiveObjects: 0.534500 ms CreateObjectMapping: 0.475200 ms MarkObjects: 4.275400 ms  DeleteObjects: 2.756800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.464 seconds
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.495 seconds
Domain Reload Profiling: 960ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (267ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (117ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (496ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (358ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (242ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (7.2 MB). Loaded Objects now: 6646.
Memory consumption went from 142.5 MB to 135.3 MB.
Total: 8.636900 ms (FindLiveObjects: 0.526400 ms CreateObjectMapping: 0.474700 ms MarkObjects: 3.811200 ms  DeleteObjects: 3.824000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.468 seconds
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.504 seconds
Domain Reload Profiling: 972ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (270ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (115ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (102ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (504ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (369ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (76ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (6.4 MB). Loaded Objects now: 6648.
Memory consumption went from 142.5 MB to 136.1 MB.
Total: 8.936500 ms (FindLiveObjects: 0.574400 ms CreateObjectMapping: 0.608800 ms MarkObjects: 4.943300 ms  DeleteObjects: 2.809200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.484 seconds
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.490 seconds
Domain Reload Profiling: 974ms
	BeginReloadAssembly (142ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (291ms)
		LoadAssemblies (225ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (132ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (118ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (490ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (358ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (235ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5977 unused Assets / (5.8 MB). Loaded Objects now: 6650.
Memory consumption went from 142.5 MB to 136.7 MB.
Total: 7.752400 ms (FindLiveObjects: 0.547700 ms CreateObjectMapping: 0.629800 ms MarkObjects: 4.135800 ms  DeleteObjects: 2.438500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5970 unused Assets / (5.8 MB). Loaded Objects now: 6650.
Memory consumption went from 142.7 MB to 136.9 MB.
Total: 6.575800 ms (FindLiveObjects: 0.507800 ms CreateObjectMapping: 0.469400 ms MarkObjects: 3.485300 ms  DeleteObjects: 2.112800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5971 unused Assets / (6.0 MB). Loaded Objects now: 6651.
Memory consumption went from 142.7 MB to 136.7 MB.
Total: 7.152100 ms (FindLiveObjects: 0.614500 ms CreateObjectMapping: 0.610800 ms MarkObjects: 3.590800 ms  DeleteObjects: 2.335400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 45586.184248 seconds.
  path: Assets/TornadoVictim.cs
  artifactKey: Guid(582ddade006b6b34db341a484c9a2d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TornadoVictim.cs using Guid(582ddade006b6b34db341a484c9a2d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdb6d63316b1a57f84f3905d3d13dd6b') in 0.0040646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5971 unused Assets / (6.4 MB). Loaded Objects now: 6653.
Memory consumption went from 143.3 MB to 136.9 MB.
Total: 7.499700 ms (FindLiveObjects: 0.537300 ms CreateObjectMapping: 0.461900 ms MarkObjects: 3.920400 ms  DeleteObjects: 2.579500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.480 seconds
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.563 seconds
Domain Reload Profiling: 1042ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (274ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (119ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (564ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (420ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (283ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (6.7 MB). Loaded Objects now: 6655.
Memory consumption went from 143.1 MB to 136.3 MB.
Total: 8.825900 ms (FindLiveObjects: 0.528900 ms CreateObjectMapping: 0.551300 ms MarkObjects: 4.165200 ms  DeleteObjects: 3.580000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 77.886707 seconds.
  path: Assets/TornadoVictim.cs
  artifactKey: Guid(582ddade006b6b34db341a484c9a2d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TornadoVictim.cs using Guid(582ddade006b6b34db341a484c9a2d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a02a152d4aaf85401b69a714cd7dc558') in 0.0011754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.603 seconds
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.630 seconds
Domain Reload Profiling: 1232ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (460ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (299ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (6.3 MB). Loaded Objects now: 6657.
Memory consumption went from 143.1 MB to 136.8 MB.
Total: 8.308500 ms (FindLiveObjects: 0.614800 ms CreateObjectMapping: 0.467600 ms MarkObjects: 4.339700 ms  DeleteObjects: 2.885800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.468 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.469 seconds
Domain Reload Profiling: 937ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (18ms)
	LoadAllAssembliesAndSetupDomain (277ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (120ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (469ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (7.2 MB). Loaded Objects now: 6659.
Memory consumption went from 143.1 MB to 135.9 MB.
Total: 10.837700 ms (FindLiveObjects: 0.957600 ms CreateObjectMapping: 0.841300 ms MarkObjects: 5.329500 ms  DeleteObjects: 3.708700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.501 seconds
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.532 seconds
Domain Reload Profiling: 1033ms
	BeginReloadAssembly (154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (286ms)
		LoadAssemblies (237ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (120ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (105ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (533ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (405ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (268ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (7.4 MB). Loaded Objects now: 6661.
Memory consumption went from 143.1 MB to 135.7 MB.
Total: 9.385000 ms (FindLiveObjects: 0.923600 ms CreateObjectMapping: 0.778200 ms MarkObjects: 3.892100 ms  DeleteObjects: 3.790300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.487 seconds
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.508 seconds
Domain Reload Profiling: 996ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (286ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (128ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (114ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (508ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (237ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (6.7 MB). Loaded Objects now: 6663.
Memory consumption went from 143.1 MB to 136.4 MB.
Total: 9.304800 ms (FindLiveObjects: 0.625200 ms CreateObjectMapping: 0.672200 ms MarkObjects: 4.424500 ms  DeleteObjects: 3.582100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.558 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.577 seconds
Domain Reload Profiling: 1135ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (333ms)
		LoadAssemblies (263ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (148ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (578ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (428ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (287ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (6.3 MB). Loaded Objects now: 6666.
Memory consumption went from 143.1 MB to 136.8 MB.
Total: 8.190000 ms (FindLiveObjects: 0.547100 ms CreateObjectMapping: 0.439700 ms MarkObjects: 4.007900 ms  DeleteObjects: 3.194300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.499 seconds
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.505 seconds
Domain Reload Profiling: 1003ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (281ms)
		LoadAssemblies (232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (116ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (101ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (506ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (382ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (72ms)
			ProcessInitializeOnLoadAttributes (263ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (6.3 MB). Loaded Objects now: 6668.
Memory consumption went from 143.1 MB to 136.8 MB.
Total: 7.977500 ms (FindLiveObjects: 0.543200 ms CreateObjectMapping: 0.492500 ms MarkObjects: 4.369700 ms  DeleteObjects: 2.571600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 854.605282 seconds.
  path: Assets/TornadoPlayerSetup.md
  artifactKey: Guid(fd7d3411755eb8f458ba384f49ede8ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TornadoPlayerSetup.md using Guid(fd7d3411755eb8f458ba384f49ede8ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c193cdf5767f2fde7c18ad8474ec2965') in 0.0134905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.566 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.619 seconds
Domain Reload Profiling: 1185ms
	BeginReloadAssembly (165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (328ms)
		LoadAssemblies (253ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (620ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (451ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (298ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 34 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (6.4 MB). Loaded Objects now: 6671.
Memory consumption went from 143.1 MB to 136.7 MB.
Total: 8.927100 ms (FindLiveObjects: 0.824100 ms CreateObjectMapping: 0.813100 ms MarkObjects: 4.033600 ms  DeleteObjects: 3.255600 ms)

Prepare: number of updated asset objects reloaded= 0
