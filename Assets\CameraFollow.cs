// ==========================================
// CAMERA FOLLOW
// ==========================================
using UnityEngine;

public class CameraFollow : MonoBehaviour
{
    [Header("Follow Settings")]
    public Transform target;
    public float smoothSpeed = 0.125f;
    public Vector3 offset = new Vector3(0, 10, -8);

    [Header("Boundaries")]
    public bool useBoundaries = true;
    public Vector3 minBounds = new Vector3(-50, 5, -50);
    public Vector3 maxBounds = new Vector3(50, 20, 50);

    void Start()
    {
        if (target == null)
        {
            TornadoController tornado = FindObjectOfType<TornadoController>();
            if (tornado != null)
            {
                target = tornado.transform;
            }
        }
    }

    void LateUpdate()
    {
        if (target == null) return;

        Vector3 desiredPosition = target.position + offset;

        // Apply boundaries
        if (useBoundaries)
        {
            desiredPosition.x = Mathf.Clamp(desiredPosition.x, minBounds.x, maxBounds.x);
            desiredPosition.y = Mathf.Clamp(desiredPosition.y, minBounds.y, maxBounds.y);
            desiredPosition.z = Mathf.Clamp(desiredPosition.z, minBounds.z, maxBounds.z);
        }

        Vector3 smoothedPosition = Vector3.Lerp(transform.position, desiredPosition, smoothSpeed);
        transform.position = smoothedPosition;

        // Look at target
        transform.LookAt(target);
    }
}