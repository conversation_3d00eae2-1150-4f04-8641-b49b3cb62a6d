{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 5116, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 5116, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 5116, "tid": 2551, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 5116, "tid": 2551, "ts": 1754769004042895, "dur": 744, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004046035, "dur": 579, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 5116, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 5116, "tid": 1, "ts": 1754769003630043, "dur": 5061, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5116, "tid": 1, "ts": 1754769003635109, "dur": 37725, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 5116, "tid": 1, "ts": 1754769003672844, "dur": 34720, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004046617, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 5116, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003628597, "dur": 8758, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003637357, "dur": 399428, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003638079, "dur": 2956, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003641039, "dur": 1396, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642437, "dur": 320, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642759, "dur": 15, "ph": "X", "name": "ProcessMessages 20541", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642775, "dur": 47, "ph": "X", "name": "ReadAsync 20541", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642825, "dur": 3, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642830, "dur": 37, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642869, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642871, "dur": 46, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642919, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642954, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642957, "dur": 32, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642991, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003642992, "dur": 28, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643023, "dur": 24, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643050, "dur": 24, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643077, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643128, "dur": 28, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643158, "dur": 34, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643195, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643223, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643259, "dur": 34, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643295, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643296, "dur": 24, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643323, "dur": 22, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643348, "dur": 30, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643380, "dur": 24, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643405, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643432, "dur": 22, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643456, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643478, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643500, "dur": 20, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643522, "dur": 25, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643548, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643574, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643598, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643618, "dur": 17, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643637, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643655, "dur": 16, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643673, "dur": 31, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643706, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643730, "dur": 19, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643751, "dur": 16, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643769, "dur": 17, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643787, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643806, "dur": 16, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643824, "dur": 16, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643842, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643865, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643884, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643908, "dur": 16, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643925, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003643943, "dur": 62, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644007, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644036, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644063, "dur": 24, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644089, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644107, "dur": 24, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644133, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644154, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644175, "dur": 22, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644199, "dur": 19, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644220, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644239, "dur": 16, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644257, "dur": 17, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644276, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644294, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644317, "dur": 28, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644348, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644369, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644391, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644410, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644434, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644453, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644472, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644490, "dur": 25, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644516, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644537, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644556, "dur": 23, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644581, "dur": 30, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644613, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644634, "dur": 39, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644675, "dur": 17, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644694, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644716, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644735, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644754, "dur": 17, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644773, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644792, "dur": 17, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644810, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644830, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644849, "dur": 23, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644874, "dur": 17, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644893, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644912, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644931, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644949, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644971, "dur": 17, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003644990, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645008, "dur": 16, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645026, "dur": 23, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645051, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645070, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645088, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645107, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645126, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645144, "dur": 17, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645162, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645163, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645181, "dur": 21, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645203, "dur": 18, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645223, "dur": 17, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645242, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645261, "dur": 17, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645280, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645300, "dur": 16, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645318, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645336, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645358, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645387, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645407, "dur": 19, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645428, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645447, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645466, "dur": 31, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645500, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645502, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645540, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645542, "dur": 145, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645692, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645734, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645736, "dur": 34, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645772, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645773, "dur": 29, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645804, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645807, "dur": 22, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645832, "dur": 22, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645858, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645884, "dur": 29, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645914, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645916, "dur": 25, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645944, "dur": 20, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645966, "dur": 27, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003645995, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646029, "dur": 38, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646070, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646096, "dur": 21, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646118, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646154, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646183, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646185, "dur": 30, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646216, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646218, "dur": 31, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646251, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646275, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646301, "dur": 20, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646323, "dur": 35, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646362, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646364, "dur": 36, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646402, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646404, "dur": 25, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646432, "dur": 22, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646455, "dur": 18, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646476, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646496, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646515, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646537, "dur": 21, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646560, "dur": 23, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646585, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646611, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646633, "dur": 17, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646652, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646671, "dur": 18, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646691, "dur": 20, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646713, "dur": 18, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646732, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646752, "dur": 18, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646772, "dur": 16, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646790, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646808, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646827, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646850, "dur": 22, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646874, "dur": 17, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646893, "dur": 17, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646912, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646933, "dur": 16, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646951, "dur": 23, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003646976, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647001, "dur": 23, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647026, "dur": 17, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647045, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647066, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647088, "dur": 27, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647117, "dur": 22, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647141, "dur": 12, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647155, "dur": 19, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647175, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647196, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647216, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647239, "dur": 14, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647255, "dur": 22, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647282, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647314, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647316, "dur": 32, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647350, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647352, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647404, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647406, "dur": 33, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647441, "dur": 23, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647467, "dur": 27, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647496, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647519, "dur": 27, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647548, "dur": 22, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647572, "dur": 21, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647596, "dur": 51, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647649, "dur": 28, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647681, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647717, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647720, "dur": 30, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647752, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647753, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647778, "dur": 20, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647800, "dur": 25, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647827, "dur": 20, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647850, "dur": 20, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647872, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647896, "dur": 26, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647925, "dur": 27, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647953, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003647974, "dur": 26, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648003, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648030, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648052, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648072, "dur": 19, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648094, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648116, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648135, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648154, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648173, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648194, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648215, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648235, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648254, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648273, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648301, "dur": 20, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648324, "dur": 23, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648349, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648370, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648394, "dur": 21, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648418, "dur": 20, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648440, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648474, "dur": 28, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648504, "dur": 25, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648531, "dur": 31, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648564, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648591, "dur": 31, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648624, "dur": 25, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648652, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648676, "dur": 25, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648704, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648705, "dur": 28, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648735, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648736, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648767, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648792, "dur": 27, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648821, "dur": 20, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648842, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648864, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648885, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648906, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648908, "dur": 27, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648937, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003648960, "dur": 90, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649052, "dur": 30, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649085, "dur": 1, "ph": "X", "name": "ProcessMessages 1225", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649086, "dur": 23, "ph": "X", "name": "ReadAsync 1225", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649111, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649134, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649155, "dur": 22, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649179, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649200, "dur": 16, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649218, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649239, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649258, "dur": 29, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649289, "dur": 18, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649308, "dur": 12, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649323, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649341, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649360, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649383, "dur": 26, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649411, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649433, "dur": 17, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649452, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649470, "dur": 16, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649487, "dur": 17, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649506, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649525, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649545, "dur": 16, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649570, "dur": 19, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649591, "dur": 16, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649609, "dur": 17, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649628, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649648, "dur": 17, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649668, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649687, "dur": 16, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649705, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649727, "dur": 18, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649748, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649771, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649792, "dur": 19, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649813, "dur": 18, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649833, "dur": 17, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649852, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649872, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649892, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649911, "dur": 20, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649933, "dur": 17, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649952, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649970, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003649988, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650008, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650029, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650049, "dur": 18, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650068, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650088, "dur": 23, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650113, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650140, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650144, "dur": 17, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650163, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650181, "dur": 17, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650202, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650221, "dur": 24, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650247, "dur": 19, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650268, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650295, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650318, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650319, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650341, "dur": 17, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650360, "dur": 17, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650380, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650405, "dur": 16, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650424, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650441, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650464, "dur": 18, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650484, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650504, "dur": 17, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650523, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650543, "dur": 16, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650560, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650580, "dur": 17, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650598, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650618, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650643, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650664, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650683, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650703, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650723, "dur": 17, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650743, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650768, "dur": 16, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650786, "dur": 17, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650805, "dur": 19, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650826, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650845, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650867, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650888, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650908, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650929, "dur": 16, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650946, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650948, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650972, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003650993, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651015, "dur": 15, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651032, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651051, "dur": 31, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651085, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651086, "dur": 20, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651109, "dur": 13, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651124, "dur": 17, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651143, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651161, "dur": 17, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651180, "dur": 21, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651204, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651222, "dur": 42, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651266, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651292, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651319, "dur": 74, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651394, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651418, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651441, "dur": 21, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651464, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651515, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651542, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651563, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651581, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651582, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651648, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651669, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651686, "dur": 19, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651708, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651726, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651777, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651798, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651822, "dur": 18, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651842, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651907, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651927, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651951, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003651970, "dur": 48, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652022, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652043, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652067, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652085, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652138, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652158, "dur": 24, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652185, "dur": 27, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652216, "dur": 59, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652278, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652306, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652308, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652338, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652359, "dur": 67, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652430, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652457, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652459, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652488, "dur": 30, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652523, "dur": 72, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652599, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652632, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652635, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652668, "dur": 26, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652697, "dur": 69, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652769, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652801, "dur": 25, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652830, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652855, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652857, "dur": 73, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652933, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652967, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003652970, "dur": 33, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653006, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653009, "dur": 29, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653042, "dur": 47, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653092, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653125, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653128, "dur": 30, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653159, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653163, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653189, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653259, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653296, "dur": 34, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653334, "dur": 26, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653363, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653420, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653455, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653459, "dur": 25, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653487, "dur": 77, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653566, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653568, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653609, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653614, "dur": 33, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653649, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653652, "dur": 27, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653685, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653714, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653740, "dur": 60, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653805, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653840, "dur": 27, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653870, "dur": 24, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653897, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653925, "dur": 49, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003653977, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654002, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654027, "dur": 24, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654053, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654075, "dur": 20, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654098, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654122, "dur": 21, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654146, "dur": 57, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654205, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654231, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654253, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654272, "dur": 18, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654291, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654294, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654372, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654399, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654400, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654424, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654449, "dur": 19, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654470, "dur": 20, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654492, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654515, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654538, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654563, "dur": 78, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654645, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654675, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654699, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654721, "dur": 81, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654804, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654829, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654849, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654876, "dur": 19, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654897, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654917, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654920, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654943, "dur": 17, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654962, "dur": 17, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003654983, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655058, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655087, "dur": 21, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655111, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655130, "dur": 58, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655192, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655214, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655237, "dur": 21, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655261, "dur": 69, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655332, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655356, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655358, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655377, "dur": 48, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655428, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655430, "dur": 23, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655456, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655478, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655479, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655497, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655517, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655588, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655614, "dur": 19, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655634, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655636, "dur": 17, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655656, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655729, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655758, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655782, "dur": 21, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655805, "dur": 75, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655883, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655908, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655909, "dur": 38, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655952, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003655972, "dur": 52, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656026, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656047, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656072, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656093, "dur": 18, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656114, "dur": 90, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656206, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656234, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656256, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656258, "dur": 15, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656275, "dur": 78, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656354, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656381, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656400, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656401, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656420, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656489, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656517, "dur": 21, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656541, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656565, "dur": 66, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656633, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656660, "dur": 19, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656680, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656699, "dur": 16, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656719, "dur": 60, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656782, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656806, "dur": 29, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656837, "dur": 18, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656858, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656920, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656944, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656966, "dur": 18, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003656987, "dur": 62, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657051, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657074, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657096, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657116, "dur": 55, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657172, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657194, "dur": 20, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657217, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657236, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657307, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657329, "dur": 18, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657351, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657371, "dur": 56, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657430, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657453, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657454, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657476, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657495, "dur": 63, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657560, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657585, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657609, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657630, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657632, "dur": 57, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657691, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657713, "dur": 7, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657722, "dur": 25, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657750, "dur": 21, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657774, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657775, "dur": 65, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657843, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657883, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657904, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657925, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003657983, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658011, "dur": 21, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658034, "dur": 16, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658053, "dur": 96, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658150, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658171, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658192, "dur": 20, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658215, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658233, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658235, "dur": 74, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658311, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658337, "dur": 17, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658357, "dur": 19, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658379, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658397, "dur": 83, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658484, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658524, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658526, "dur": 30, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658559, "dur": 57, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658619, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658650, "dur": 19, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658672, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658675, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658692, "dur": 68, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658762, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658783, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658802, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658822, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658843, "dur": 71, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658915, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658944, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658946, "dur": 22, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658970, "dur": 18, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003658990, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659050, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659075, "dur": 19, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659097, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659116, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659118, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659148, "dur": 29, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659180, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659182, "dur": 24, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659210, "dur": 18, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659231, "dur": 55, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659288, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659309, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659331, "dur": 17, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659349, "dur": 2, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659352, "dur": 18, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659373, "dur": 19, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659394, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659418, "dur": 27, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659450, "dur": 33, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659486, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659577, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659612, "dur": 34, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659648, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659651, "dur": 42, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659695, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659697, "dur": 31, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659732, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659734, "dur": 28, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659770, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659771, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659801, "dur": 104, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659908, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003659928, "dur": 118, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660049, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660081, "dur": 330, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660415, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660466, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660495, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660514, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660537, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660557, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660592, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660603, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660634, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660656, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660679, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660698, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660716, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660734, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660752, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660772, "dur": 35, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660811, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660836, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660857, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660877, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660894, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660913, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660930, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660947, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660964, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003660975, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661010, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661033, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661035, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661055, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661084, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661106, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661128, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661148, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661169, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661189, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661207, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661223, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661266, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661306, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661333, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661350, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661377, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661398, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661414, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661432, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661435, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661453, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661475, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661493, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661511, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661531, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661550, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661567, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661583, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661600, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661611, "dur": 5, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661617, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661635, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661653, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661674, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661710, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661729, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661746, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661770, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661780, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661798, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661820, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661839, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661842, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661871, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661873, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661899, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661901, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661918, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661930, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661948, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661968, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003661987, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662005, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662007, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662036, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662055, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662059, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662081, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662102, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662122, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662141, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662162, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662183, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662199, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662219, "dur": 14, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662236, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662255, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662273, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662290, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662292, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662310, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662327, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662346, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662363, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662382, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662399, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662417, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662435, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662453, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662470, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662472, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662498, "dur": 13, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662514, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662533, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662552, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662571, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662591, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662610, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662629, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662647, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662666, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662684, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662704, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662725, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662747, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662749, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662772, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662793, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662813, "dur": 15, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662830, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662855, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662879, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662881, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662904, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662923, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662924, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662941, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662959, "dur": 15, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662977, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003662995, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663014, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663027, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663054, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663076, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663078, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663099, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663123, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663141, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663159, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663177, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663193, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663208, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663229, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663231, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663255, "dur": 58, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663317, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663347, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663351, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663375, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663405, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663407, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663433, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663461, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663463, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663492, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663557, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663582, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663783, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663810, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003663832, "dur": 6015, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003669850, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003669853, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003669871, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003669920, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003669938, "dur": 986, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003670929, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003670944, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003670965, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003671101, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003671126, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003671148, "dur": 7424, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678579, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678607, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678643, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678679, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678681, "dur": 113, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678799, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003678840, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679233, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679260, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679261, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679273, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679435, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679464, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679466, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679487, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679504, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679621, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679638, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679655, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679672, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679806, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679823, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679843, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679860, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679877, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679895, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679922, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679946, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679949, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679971, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679980, "dur": 4, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003679986, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680038, "dur": 212, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680251, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680281, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680283, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680384, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680400, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680537, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680566, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680632, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680661, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680682, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680705, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680727, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680747, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680817, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680834, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680862, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680880, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680896, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680918, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680935, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680957, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680960, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003680980, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681003, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681025, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681044, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681061, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681134, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681154, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681174, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681191, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681217, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681234, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681252, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681268, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681337, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681360, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681452, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681475, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681496, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681512, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681534, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681554, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681580, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681598, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681620, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681637, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681655, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681671, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681712, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681733, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681760, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681779, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681916, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681940, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003681961, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682067, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682090, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682108, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682125, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682148, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682171, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682190, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682217, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682237, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682304, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682323, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682350, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682368, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682414, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682442, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682467, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682574, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682600, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682628, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682648, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682672, "dur": 12, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682687, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682726, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682743, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682767, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682774, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682799, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682815, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682877, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682902, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003682936, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683052, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683085, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683107, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683264, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683288, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683307, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683336, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683353, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683639, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003683659, "dur": 534, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684196, "dur": 38, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684238, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684241, "dur": 24, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684268, "dur": 404, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684676, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684701, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684703, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684735, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684833, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684850, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684879, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684882, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684921, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003684950, "dur": 321, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685275, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685309, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685339, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685374, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685405, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685432, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685459, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685494, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685595, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685614, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685963, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685992, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003685997, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686139, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686165, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686270, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686300, "dur": 496, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686800, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686830, "dur": 162, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686995, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003686998, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003687027, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003687029, "dur": 286, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003687320, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003687352, "dur": 693, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003688049, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003688078, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003688080, "dur": 51033, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003739119, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003739122, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003739166, "dur": 1594, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003740763, "dur": 6553, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747323, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747348, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747387, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747421, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747423, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747452, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747470, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747472, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747499, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747589, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747612, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003747615, "dur": 557, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003748175, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003748194, "dur": 760, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003748959, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003748992, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749025, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749055, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749084, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749169, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749200, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749221, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749392, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749412, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749529, "dur": 242, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749776, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003749804, "dur": 477, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750286, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750315, "dur": 525, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750844, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750874, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750941, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003750961, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751010, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751030, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751379, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751402, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751605, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751625, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751795, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751816, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751883, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003751900, "dur": 523, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752427, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752449, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752633, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752651, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752668, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752983, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003752995, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753125, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753146, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753180, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753198, "dur": 361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753562, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753585, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753610, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753627, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753742, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003753760, "dur": 683, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754447, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754470, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754755, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754774, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003754968, "dur": 515, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755488, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755516, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755629, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755656, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755657, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755678, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755699, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755716, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755736, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755754, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755772, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755802, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755820, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755836, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755856, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755872, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755890, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755908, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755925, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755943, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755967, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003755986, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756005, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756024, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756042, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756059, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756081, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756103, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756124, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756144, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756164, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756182, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756201, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756228, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756230, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756263, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756283, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756301, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756322, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756341, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756365, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756386, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756407, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756482, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756504, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756530, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756549, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756570, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756587, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756605, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756622, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756641, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756663, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756753, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003756785, "dur": 344, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757133, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757163, "dur": 300, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757469, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757501, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757539, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757569, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757594, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757626, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003757652, "dur": 71682, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003829339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003829342, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003829387, "dur": 22, "ph": "X", "name": "ProcessMessages 1134", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003829410, "dur": 14176, "ph": "X", "name": "ReadAsync 1134", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003843590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003843593, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003843636, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003843639, "dur": 31763, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875408, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875458, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875462, "dur": 81, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875546, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875577, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003875579, "dur": 92522, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003968105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003968110, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003968131, "dur": 14, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003968146, "dur": 16186, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003984337, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003984339, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003984375, "dur": 17, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003984393, "dur": 1172, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003985568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003985570, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003985597, "dur": 12024, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003997625, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003997630, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769003997669, "dur": 4694, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004002367, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004002370, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004002402, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004002405, "dur": 1605, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004004016, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004004061, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004004079, "dur": 21097, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004025179, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004025182, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004025204, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004025206, "dur": 859, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026073, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026111, "dur": 18, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026131, "dur": 467, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026603, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004026632, "dur": 438, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 5116, "tid": 12884901888, "ts": 1754769004027073, "dur": 9139, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004046627, "dur": 1175, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 5116, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 5116, "tid": 8589934592, "ts": 1754769003626494, "dur": 81101, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 5116, "tid": 8589934592, "ts": 1754769003707597, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 5116, "tid": 8589934592, "ts": 1754769003707603, "dur": 1016, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004047804, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 5116, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 5116, "tid": 4294967296, "ts": 1754769003610626, "dur": 426854, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 5116, "tid": 4294967296, "ts": 1754769003613178, "dur": 8112, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 5116, "tid": 4294967296, "ts": 1754769004037677, "dur": 3325, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 5116, "tid": 4294967296, "ts": 1754769004039640, "dur": 73, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 5116, "tid": 4294967296, "ts": 1754769004041054, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004047811, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754769003634712, "dur": 1839, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769003636563, "dur": 929, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769003637606, "dur": 64, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754769003637670, "dur": 321, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769003639114, "dur": 654, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_4DBF07B00C0E2925.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754769003640710, "dur": 1724, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754769003647272, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754769003638040, "dur": 21562, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769003659617, "dur": 366183, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769004025801, "dur": 219, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769004026174, "dur": 68, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769004026277, "dur": 1540, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754769003638163, "dur": 21465, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003659669, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003659774, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_155F684945DC9544.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003660116, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003660201, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_70EA4698559D2BA8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003660320, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_D58CD4CE283171EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003660630, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_E60E6602041343C2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003660704, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003660891, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003660992, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_1643675A43A3052C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003661453, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003661676, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754769003661818, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754769003662026, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754769003662268, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754769003662980, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754769003663338, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003663494, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003664017, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003664447, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003664883, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003665319, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003665751, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003666270, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003666681, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003667136, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003667564, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003668012, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003668874, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003669402, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003669944, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003670395, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003671074, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\VariableUnit.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754769003670963, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003671929, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003672369, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003672798, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003673278, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003673726, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003674155, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003674595, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003675028, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003675457, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003675872, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003676378, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003676787, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003677216, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003677636, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003677969, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003678332, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003679159, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003679344, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003679491, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003679648, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003679737, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003679896, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003680538, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003680666, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003680823, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003680913, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003681090, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003681518, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003681656, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003681900, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003682368, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003682488, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003682803, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003682946, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003683496, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003683597, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003683738, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003684268, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003684380, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754769003684508, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003684936, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003685068, "dur": 1470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003686538, "dur": 59149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003745689, "dur": 1465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003747156, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003747285, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003749305, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003751411, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003751479, "dur": 1737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003753217, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003753299, "dur": 1967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003755266, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003756090, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754769003756339, "dur": 77078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003833493, "dur": 38766, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754769003833419, "dur": 40171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769003874797, "dur": 161, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754769003875653, "dur": 108362, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754769004001901, "dur": 22909, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754769004001886, "dur": 22926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754769004024835, "dur": 911, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754769003638249, "dur": 21396, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003659667, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003659735, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_9220C28E170BA257.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003660388, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003660652, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E4D2CEE8E2AEA62D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003660772, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_E4D2CEE8E2AEA62D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003660938, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_8A027532CAAA8207.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003661305, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_FB84D350566514EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003661422, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003661472, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003661610, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754769003662145, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754769003662963, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003663111, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003663914, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003664424, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003664951, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003665387, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003665841, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003666319, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003666758, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003667190, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003667619, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003668141, "dur": 1026, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@4e35da2bf8a6\\Editor\\Data\\Graphs\\VertexColorMaterialSlot.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754769003668041, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003669468, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003669899, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003670329, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003670790, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003671270, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003671696, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003672129, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003672549, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003672982, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003673386, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003673822, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003674251, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003674684, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003675134, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003675564, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003675976, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003676466, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003676899, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003677356, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003677823, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003678316, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003678989, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003679189, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003679602, "dur": 970, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003680607, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754769003680814, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003681727, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003681860, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003682367, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003683071, "dur": 3457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003686528, "dur": 58806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003745336, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003747019, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003747088, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003747150, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003748806, "dur": 1729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003750542, "dur": 2461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003753004, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003753248, "dur": 2575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003755873, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754769003756073, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754769003756300, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754769003757196, "dur": 268584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003638308, "dur": 21390, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003659708, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_693BF34AA310D04C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003660897, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003661931, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754769003662301, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754769003662684, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1754769003662946, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003663583, "dur": 1860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\Tests\\TestFixture\\InputTestRuntime.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754769003663106, "dur": 2585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003666544, "dur": 1971, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@18be219df6cb\\Editor\\Settings\\PropertyDrawers\\URPRenderGraphPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754769003665691, "dur": 2824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003668924, "dur": 2682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@4e35da2bf8a6\\Editor\\Data\\Graphs\\CubemapInputMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754769003668516, "dur": 3136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003671652, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003672110, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003672562, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003672987, "dur": 1121, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@f3fac7af1578\\Runtime\\TMP\\TMPro_ExtensionMethods.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754769003672987, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003674519, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003674982, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003675463, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003675921, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003676439, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003676885, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003677303, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003677828, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003678327, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003678990, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003679097, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003679568, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003680085, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003680243, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003680846, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003680949, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003681187, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003681268, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003681726, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003681794, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003682138, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003682305, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003683420, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003683541, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003683686, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003684278, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003684416, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003684574, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003685036, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003685141, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754769003685270, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003685579, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003685665, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003686532, "dur": 58814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003745347, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003746912, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003746983, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003748525, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003748710, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003750938, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003751047, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003752662, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003752816, "dur": 1904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754769003754721, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003754994, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003755297, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003755438, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003755736, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003755888, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769003756074, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754769003756468, "dur": 245424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754769004001934, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754769004001893, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754769004002042, "dur": 1660, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1754769004003706, "dur": 22097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003638294, "dur": 21367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003659670, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_D8ADFD8AB9EE438E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003660239, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_4DBF07B00C0E2925.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003660597, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_EBA177BF5C357AC1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003660769, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_61F6AF8F2D44AABF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003660935, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_C2F0C5B835A2B973.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003661773, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003661898, "dur": 8636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003670535, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003670647, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003670786, "dur": 7364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003678151, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003678331, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003678474, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003678823, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003678986, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003679115, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003679570, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003679678, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003679810, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003680262, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003680397, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003680760, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003680929, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003681074, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003681449, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003681819, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003682415, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003682789, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754769003682944, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003683310, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003683434, "dur": 3092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003686527, "dur": 58813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003745342, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003746982, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003747088, "dur": 1692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003748781, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003748852, "dur": 1717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003750570, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003750628, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003752561, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003752668, "dur": 1700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003754368, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003754442, "dur": 1803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754769003756318, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754769003757311, "dur": 268466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003638332, "dur": 21382, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003659724, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_2EDA0E5DBD30B8D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003660272, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_963B977EC9725DF9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003660579, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003660799, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_6559BE7C8BD35D96.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003660893, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_185C8CAB4672C07A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003661172, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_24730170E55E80B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003661752, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1754769003661902, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754769003662472, "dur": 565, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1754769003663058, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003663156, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003663821, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003664256, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003664664, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003665087, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003665536, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003665975, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003666403, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003666843, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003667314, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003667740, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003668476, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003669051, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003669486, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003669952, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003670384, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003670813, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003671246, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003671728, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003672169, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003672605, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003673311, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003673761, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003674356, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003674778, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003675205, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003675687, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003676121, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003676545, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003676989, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003677411, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003677830, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003678275, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003678980, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003679146, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003679488, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003679618, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003679734, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003679839, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003680474, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003680551, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003680741, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003681289, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003681444, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003681602, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003681951, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003682036, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003682413, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003682805, "dur": 1581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003684388, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003684553, "dur": 1210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003685837, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003685946, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003686422, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003686526, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1754769003686664, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003686919, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003686991, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003687662, "dur": 51, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003688373, "dur": 140646, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003833856, "dur": 9244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754769003833411, "dur": 9745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003843163, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003843247, "dur": 28998, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1754769003843244, "dur": 30422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003875046, "dur": 166, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1754769003875669, "dur": 92113, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1754769003985105, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754769003985082, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1754769003985241, "dur": 40594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003638370, "dur": 21369, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003659746, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_4BBC75A8142BFE40.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003660378, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003660436, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_90429E1B574A9218.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003660494, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003660581, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0970C277CDB87A8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003660767, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0970C277CDB87A8C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003660933, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_1C6987EFBA49C716.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003661399, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003661580, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003661686, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003661962, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003662066, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1754769003663115, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003663767, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003664189, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003664632, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003665056, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003665468, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003665901, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003666442, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003666878, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003667317, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003667736, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003668514, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003669090, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003669644, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003670094, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003670543, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003671029, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003671578, "dur": 5152, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\OnDestroy.cs"}}, {"pid": 12345, "tid": 6, "ts": 1754769003671452, "dur": 5569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003677021, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003677490, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003677827, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003678289, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003679102, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003679307, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003679933, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003680063, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003680399, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003680517, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003680636, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003681729, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003681831, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003682394, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003682801, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003682918, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003683023, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003683517, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003683638, "dur": 2885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003686524, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1754769003686687, "dur": 60422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003747110, "dur": 1891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003749002, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003749078, "dur": 2941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003752020, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003752120, "dur": 3746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1754769003755867, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1754769003756135, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1754769003756504, "dur": 269296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003638413, "dur": 21360, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003659780, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_7C47C5441FF97428.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003660102, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003660233, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003660591, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E47B260DB565F429.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003660722, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003660784, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_B57CF891779FB50D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003660886, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003661363, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003661799, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754769003661879, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003662421, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1754769003662994, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003663248, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003663878, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003664347, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003664772, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003665191, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003665625, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003666066, "dur": 1268, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@18be219df6cb\\Editor\\2D\\Shadows\\ShadowCaster2DShapeTool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754769003666065, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003667753, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003668619, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003669153, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003669597, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003670047, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003670478, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003670915, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003671331, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003671769, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003672211, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003672649, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003673166, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003673622, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003674072, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003674516, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003674941, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003675375, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003675815, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003676717, "dur": 1124, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\matrix.gen.cs"}}, {"pid": 12345, "tid": 7, "ts": 1754769003676272, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003677842, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003678326, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003678977, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003679117, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003680171, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003680364, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003680427, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003680568, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003681505, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003681653, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003681828, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003682664, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003682792, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003682974, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003683424, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003683539, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003683692, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003684254, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003684379, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1754769003684519, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003684881, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003684980, "dur": 1545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003686525, "dur": 21796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003710642, "dur": 286, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1754769003710928, "dur": 1328, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1754769003712256, "dur": 90, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.46f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1754769003708323, "dur": 4029, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003712352, "dur": 36296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003748650, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003751162, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003751572, "dur": 1635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003753208, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003753263, "dur": 1972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1754769003755239, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003755411, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003755580, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003755740, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003756006, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1754769003756139, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1754769003756511, "dur": 269268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003638448, "dur": 21347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003659805, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_97400A7B90B57EF6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003660138, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003660210, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_8D5E0434B2505E22.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003660895, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003661017, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_743C304FD6F18044.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003661669, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754769003661721, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003662996, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1754769003663355, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003663491, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003663991, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003664416, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003664865, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003665269, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003665702, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003666172, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003666622, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003667048, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003667471, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003668508, "dur": 2602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@4e35da2bf8a6\\Editor\\Data\\Interfaces\\IMayRequireNormal.cs"}}, {"pid": 12345, "tid": 8, "ts": 1754769003667899, "dur": 3358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003671257, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003671858, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003672313, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003672752, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003673228, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003673651, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003674082, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003674521, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003674953, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003675407, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003675883, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003676393, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003676876, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003677295, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003677822, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003678337, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003678985, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003679140, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003679509, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003679672, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003679828, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003680420, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003680505, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1754769003680641, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003681075, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003681356, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003681800, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003682373, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003683075, "dur": 3451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003686526, "dur": 25833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003712360, "dur": 32983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003745350, "dur": 1668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003747019, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003747090, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003747290, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003749158, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003749219, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003751289, "dur": 1527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003752870, "dur": 1711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003754581, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003754653, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1754769003757026, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003757151, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1754769003757218, "dur": 268564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003638478, "dur": 21348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003659832, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_AEF6DA0D961119EB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003660889, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003660986, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_2D411DFE95898EE8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003661779, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003661909, "dur": 7531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003669441, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003669527, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003669613, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003670070, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003670552, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003671087, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003671523, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003672072, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003672505, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003673284, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003673748, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003674396, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003674819, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003675405, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003675826, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003676296, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003676732, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003677593, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003678077, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003678328, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003679121, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003679230, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003679346, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003680491, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003680713, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003680847, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003680933, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003681666, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003681757, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003681891, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003682237, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003682368, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003682800, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1754769003682949, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003683249, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003683378, "dur": 3153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003686531, "dur": 59729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003746262, "dur": 1544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003747862, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003749854, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003749964, "dur": 1846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003751811, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003752321, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1754769003755128, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003755495, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 9, "ts": 1754769003755630, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003755694, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1754769003755954, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754769003756104, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1754769003756482, "dur": 269317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003638519, "dur": 21326, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003659852, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_3B7D5FFDEE782BA5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003660906, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_42DBBF325BE9E86D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003661413, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003661869, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1754769003662011, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754769003662413, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1754769003663124, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003663800, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003664265, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003664621, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003665065, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003665512, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003665999, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003666526, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003667057, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003667811, "dur": 709, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@4e35da2bf8a6\\Editor\\Data\\Nodes\\Math\\Derivative\\DDXYNode.cs"}}, {"pid": 12345, "tid": 10, "ts": 1754769003667563, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003668690, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003669226, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003669666, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003670147, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003670574, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003671024, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003671467, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003671920, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003672360, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003672790, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003673252, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003673766, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003674229, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003674718, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003675154, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003675725, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003676248, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003676742, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003677174, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003677602, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003678051, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003678281, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003678982, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003679155, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003679545, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003679658, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003679816, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003680281, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003680369, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003680991, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003681205, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003681274, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003681337, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003681654, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1754769003681781, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003682145, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003682364, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754769003682897, "dur": 104, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003683366, "dur": 55426, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1754769003745344, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003747017, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003747102, "dur": 1563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003748665, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003748756, "dur": 1559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003750316, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003750527, "dur": 1720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003752248, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003752339, "dur": 1540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003753880, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003754130, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1754769003755877, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003756077, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1754769003756439, "dur": 228648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1754769003985132, "dur": 12108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754769003985088, "dur": 12154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1754769003997284, "dur": 28540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003638547, "dur": 21305, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003659858, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_46D24DAE91E5F85D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003660231, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_1AE8D7BE1835D1DF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003660748, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_2C87DF68232A6C5E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003660902, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003661343, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1754769003661424, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_570D95476513426D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003661818, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1754769003662129, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1754769003662624, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003662971, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003663461, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003663927, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003664338, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003664741, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003665156, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003665577, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003666024, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003666460, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003666888, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003667317, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003667742, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003668579, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003669036, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003669479, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003669891, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003670346, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003670816, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003671232, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003671675, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003672109, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003672552, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003672975, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003673421, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003673913, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003674487, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003675249, "dur": 961, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Runtime\\Utilities\\NotificationUtilities.cs"}}, {"pid": 12345, "tid": 11, "ts": 1754769003674927, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003676302, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003676717, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003677166, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003677645, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003677767, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003677829, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003678275, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003678979, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003679141, "dur": 393, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003679534, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003680584, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003680681, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003680789, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003680852, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003681980, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003682133, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003682253, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003683223, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003683377, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003683499, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003683837, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003683945, "dur": 2576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003686522, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1754769003686681, "dur": 58669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003745352, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003746958, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003747095, "dur": 1718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003748814, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003748905, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003751514, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003751587, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003753290, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003753433, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1754769003755464, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1754769003756070, "dur": 423, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1754769003756494, "dur": 269307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003638583, "dur": 21277, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003659861, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EF08C9BFEF53BB02.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003660218, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_B022CC639863D454.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003660361, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_99E496FAFFF783EB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003660534, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003660688, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003660827, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003661003, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66CBC33F697D8A9D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003661318, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_C0160F5BD9F90C2E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003661890, "dur": 415, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1754769003662307, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003662521, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003662589, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003662735, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003662839, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003663454, "dur": 1029, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@18be219df6cb\\Editor\\Converter\\PPv2\\EffectConverters\\VignetteConverter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754769003663101, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003665392, "dur": 3129, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\AnimationPlayableAssetInspector.cs"}}, {"pid": 12345, "tid": 12, "ts": 1754769003665010, "dur": 3554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003668564, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003669015, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003669479, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003669914, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003670343, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003670812, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003671251, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003671694, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003672135, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003672574, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003672998, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003673446, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003673881, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003674321, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003674738, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003675162, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003675571, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003675993, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003676445, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003676880, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003677291, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003677828, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003678284, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003678986, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003679150, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003679530, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003679963, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003680350, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003680739, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003680901, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003681002, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003681645, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003681850, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003681986, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003682464, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003682593, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003683060, "dur": 1362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003684424, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1754769003684527, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003684599, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003684963, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003685071, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003686531, "dur": 58807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003745340, "dur": 1767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003747108, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003747176, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003748557, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003748634, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003750502, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003750699, "dur": 1900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003752599, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003752813, "dur": 2048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003754862, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003754957, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1754769003757167, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1754769003757303, "dur": 268475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754769004033519, "dur": 1809, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 5116, "tid": 2551, "ts": 1754769004048186, "dur": 2090, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 5116, "tid": 2551, "ts": 1754769004050338, "dur": 1621, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 5116, "tid": 2551, "ts": 1754769004045136, "dur": 7418, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}