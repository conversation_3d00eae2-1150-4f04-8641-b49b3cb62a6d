// ==========================================
// GELİŞTİRİLMİŞ DESTRUCTIBLE OBJECT
// ==========================================
using UnityEngine;

public class DestructibleObject : MonoBehaviour
{
    [Header("Destruction Settings")]
    public float RequiredSize = 1f;
    public float PowerValue = 0.3f;
    public int ScoreValue = 10;
    public bool IsDestroyed = false;

    [Header("Physics Settings")]
    public float objectMass = 1f;           // Nesne ağırlığı
    public bool canBeCaught = true;         // Tornado tarafından yakalanabilir mi?

    [Header("Effects")]
    public GameObject destructionEffect;
    public AudioClip destructionSound;

    private AudioSource audioSource;
    private Rigidbody objRigidbody;
    private bool isInTornado = false;

    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }

        // Rigidbody ekle
        objRigidbody = GetComponent<Rigidbody>();
        if (objRigidbody == null)
        {
            objRigidbody = gameObject.AddComponent<Rigidbody>();
        }

        objRigidbody.mass = objectMass;
    }

    public void DestroyObject()
    {
        if (IsDestroyed) return;
        IsDestroyed = true;

        // Yıkım efekti
        if (destructionEffect != null)
        {
            Instantiate(destructionEffect, transform.position, Quaternion.identity);
        }

        // Yıkım sesi
        if (destructionSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(destructionSound);
        }

        // Görsel olarak gizle
        GetComponent<Renderer>().enabled = false;
        GetComponent<Collider>().enabled = false;

        // 3 saniye sonra tamamen yok et
        StartCoroutine(DestroyAfterDelay(3f));
    }

    System.Collections.IEnumerator DestroyAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        Destroy(gameObject);
    }

    public void SetInTornado(bool inTornado)
    {
        isInTornado = inTornado;

        if (isInTornado)
        {
            // Tornado'da iken physics ayarları
            objRigidbody.useGravity = false;
            objRigidbody.linearDamping = 10f;
        }
        else
        {
            // Normal physics ayarları
            objRigidbody.useGravity = true;
            objRigidbody.linearDamping = 0.5f;
        }
    }
}

