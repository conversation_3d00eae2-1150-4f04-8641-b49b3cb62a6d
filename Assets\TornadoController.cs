// ==========================================
// TORNADO CONTROLLER - GERÇEK FİZİK VERSİYONU
// ==========================================
using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class TornadoController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 360f;

    [Header("Size Settings")]
    public float currentSize = 1f;
    public float maxSize = 10f;
    public float minSize = 0.5f;

    [Header("Tornado Physics")]
    public float suctionRange = 5f;          // Çekme menzili
    public float suctionForce = 500f;        // Çekme kuvveti
    public float liftForce = 300f;           // Yukarı kaldırma
    public float orbitSpeed = 5f;            // Dönüş hızı
    public float throwForce = 1000f;         // Fırlatma kuvveti
    public float holdDuration = 5f;          // Ka<PERSON> saniye tutacak

    [Header("Player Physics")]
    public float playerSuctionForce = 800f;  // Oyuncu çekme kuvveti
    public float playerOrbitRadius = 3f;     // Oyuncu dönüş yarıçapı
    public float playerOrbitHeight = 4f;     // Oyuncu yüksekliği
    public string playerTag = "Player";      // Oyuncu tag'i

    [Header("Detection Settings")]
    public LayerMask destructibleLayer = 1;

    [Header("Effects")]
    public ParticleSystem tornadoParticles;
    public AudioSource tornadoSound;

    [Header("Debug")]
    public bool showDebugInfo = true;

    // Private variables
    private Vector3 movement;
    private Rigidbody rb;
    private GameManager gameManager;
    private Transform tornadoMesh;

    // Tornado'nun içindeki nesneler
    private List<TornadoVictim> caughtObjects = new List<TornadoVictim>();

    // Oyuncu yakalama sistemi
    private GameObject player;
    private bool playerCaught = false;
    private float playerCaptureTime;
    private float playerOrbitAngle;
    private Rigidbody playerRb;

    void Start()
    {
        rb = GetComponent<Rigidbody>();
        gameManager = FindObjectOfType<GameManager>();

        // Oyuncuyu bul
        player = GameObject.FindGameObjectWithTag(playerTag);
        if (player != null)
        {
            playerRb = player.GetComponent<Rigidbody>();
            if (playerRb == null)
            {
                playerRb = player.AddComponent<Rigidbody>();
                playerRb.mass = 70f; // İnsan ağırlığı
            }
        }

        if (transform.childCount > 0)
        {
            tornadoMesh = transform.GetChild(0);
        }

        UpdateTornadoSize();
        UpdateParticleSystem();

        if (showDebugInfo)
        {
            Debug.Log("🌪️ Tornado başlatıldı - Başlangıç boyutu: " + currentSize);
            if (player != null)
                Debug.Log("🎮 Oyuncu bulundu: " + player.name);
            else
                Debug.Log("⚠️ Oyuncu bulunamadı! Tag: " + playerTag);
        }
    }

    void Update()
    {
        HandleInput();
        RotateTornado();
        ApplyTornadoPhysics();
        ApplyPlayerPhysics();
        UpdateCaughtObjects();
    }

    void FixedUpdate()
    {
        MoveTornado();
    }

    void HandleInput()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        movement = new Vector3(horizontal, 0, vertical).normalized;
    }

    void MoveTornado()
    {
        if (movement.magnitude > 0)
        {
            Vector3 velocity = movement * moveSpeed;
            velocity.y = rb.linearVelocity.y;
            rb.linearVelocity = velocity;
        }
        else
        {
            rb.linearVelocity = new Vector3(0, rb.linearVelocity.y, 0);
        }
    }

    void RotateTornado()
    {
        if (tornadoMesh != null)
        {
            tornadoMesh.Rotate(0, rotationSpeed * Time.deltaTime, 0);
        }
    }

    void ApplyTornadoPhysics()
    {
        // Tornado etki alanındaki TÜM nesneleri bul (sadece destructible değil!)
        Collider[] objectsInRange = Physics.OverlapSphere(transform.position, suctionRange);

        foreach (Collider obj in objectsInRange)
        {
            // Tornado'nun kendisini atla
            if (obj.gameObject == gameObject) continue;

            // Player'ı atla (ayrı sistem var)
            if (obj.gameObject == player) continue;

            Rigidbody objRb = obj.GetComponent<Rigidbody>();
            if (objRb == null)
            {
                objRb = obj.gameObject.AddComponent<Rigidbody>();
                objRb.mass = Random.Range(0.5f, 3f); // Rastgele ağırlık
            }

            // Tornado'nun içinde mi kontrol et
            TornadoVictim victim = caughtObjects.Find(v => v.obj == obj.gameObject);

            // GERÇEK TORNADO GİBİ - HER ŞEYİ YAKALA!
            if (victim == null)
            {
                // İlk kez yakalandı - HER NESNEYİ yakala
                StartCatchingObject(obj.gameObject, obj.GetComponent<DestructibleObject>());
            }
            else
            {
                // Zaten yakalanmış - tornado içinde döndür
                SpinObjectInTornado(objRb, victim);
            }
        }
    }

    void StartCatchingObject(GameObject obj, DestructibleObject destructible)
    {
        TornadoVictim newVictim = new TornadoVictim
        {
            obj = obj,
            destructible = destructible,
            captureTime = Time.time,
            orbitAngle = Random.Range(0f, 360f),
            orbitRadius = Random.Range(1f, 3f),
            orbitHeight = Random.Range(2f, 6f)
        };

        caughtObjects.Add(newVictim);

        // Nesneyi havaya kaldır
        Rigidbody objRb = obj.GetComponent<Rigidbody>();
        objRb.useGravity = false; // Gravity'yi kapat
        objRb.linearDamping = 5f; // Hava direnci artır

        if (showDebugInfo)
        {
            Debug.Log("🌪️ Nesne yakalandı: " + obj.name);
        }

        // Güç kazan (sadece destructible nesnelerden)
        if (destructible != null)
        {
            GainPower(destructible.PowerValue);
            if (gameManager != null)
            {
                gameManager.AddScore(destructible.ScoreValue);
            }
        }
        else
        {
            // Destructible olmayan nesneler için küçük güç kazanımı
            GainPower(0.1f);
            if (gameManager != null)
            {
                gameManager.AddScore(5);
            }
        }
    }

    void SpinObjectInTornado(Rigidbody objRb, TornadoVictim victim)
    {
        // Orbit hesapla
        victim.orbitAngle += orbitSpeed * currentSize * Time.deltaTime;

        // Hedef pozisyon (tornado'nun etrafında dönüyor)
        Vector3 orbitPos = new Vector3(
            Mathf.Cos(victim.orbitAngle * Mathf.Deg2Rad) * victim.orbitRadius,
            victim.orbitHeight,
            Mathf.Sin(victim.orbitAngle * Mathf.Deg2Rad) * victim.orbitRadius
        );

        Vector3 targetPos = transform.position + orbitPos;

        // Pozisyona doğru kuvvet uygula
        Vector3 direction = (targetPos - objRb.transform.position);
        objRb.AddForce(direction * suctionForce * Time.deltaTime, ForceMode.Force);

        // Spin efekti
        objRb.AddTorque(Vector3.up * orbitSpeed * 50f * Time.deltaTime, ForceMode.Force);

        // Zamanı dolmuş mu kontrol et
        if (Time.time - victim.captureTime >= holdDuration)
        {
            ThrowObject(objRb, victim);
        }
    }

    void ThrowObject(Rigidbody objRb, TornadoVictim victim)
    {
        // Fırlatma yönü (rastgele ama yukarı ağırlıklı)
        Vector3 throwDirection = new Vector3(
            Random.Range(-1f, 1f),
            Random.Range(1f, 2f),    // Yukarı ağırlıklı
            Random.Range(-1f, 1f)
        ).normalized;

        // Fırlatma kuvveti
        Vector3 throwVelocity = throwDirection * (throwForce + currentSize * 100f);

        // Gravity'yi aç ve fırlat
        objRb.useGravity = true;
        objRb.linearDamping = 0.5f;
        objRb.linearVelocity = Vector3.zero; // Önceki hızı sıfırla
        objRb.AddForce(throwVelocity, ForceMode.Impulse);

        // Random spin
        Vector3 randomTorque = new Vector3(
            Random.Range(-500f, 500f),
            Random.Range(-500f, 500f),
            Random.Range(-500f, 500f)
        );
        objRb.AddTorque(randomTorque, ForceMode.Impulse);

        // Nesneyi yok et (sadece destructible ise)
        if (victim.destructible != null)
        {
            victim.destructible.DestroyObject();
        }

        // Listeden çıkar
        caughtObjects.Remove(victim);

        if (showDebugInfo)
        {
            Debug.Log("💥 Nesne fırlatıldı: " + victim.obj.name + " - Yön: " + throwDirection);
        }
    }



    void ApplyPlayerPhysics()
    {
        if (player == null || playerRb == null) return;

        float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);

        // Oyuncu tornado menzilinde mi?
        if (distanceToPlayer <= suctionRange)
        {
            if (!playerCaught)
            {
                // Oyuncuyu yakala
                StartCatchingPlayer();
            }
            else
            {
                // Oyuncuyu tornado içinde döndür
                SpinPlayerInTornado();
            }
        }
        else if (playerCaught)
        {
            // Oyuncu çok uzaklaştı, bırak
            ReleasePlayer();
        }
    }

    void StartCatchingPlayer()
    {
        playerCaught = true;
        playerCaptureTime = Time.time;
        playerOrbitAngle = Random.Range(0f, 360f);

        // Oyuncunun gravity'sini kapat
        playerRb.useGravity = false;
        playerRb.linearDamping = 5f;

        if (showDebugInfo)
        {
            Debug.Log("🌪️ OYUNCU YAKALANDI! Tornado etkisinde!");
        }
    }

    void SpinPlayerInTornado()
    {
        // Orbit açısını güncelle
        playerOrbitAngle += orbitSpeed * currentSize * Time.deltaTime * 2f; // Oyuncu daha hızlı dönsün

        // Hedef pozisyon hesapla (tornado'nun etrafında)
        Vector3 orbitPos = new Vector3(
            Mathf.Cos(playerOrbitAngle * Mathf.Deg2Rad) * playerOrbitRadius,
            playerOrbitHeight,
            Mathf.Sin(playerOrbitAngle * Mathf.Deg2Rad) * playerOrbitRadius
        );

        Vector3 targetPos = transform.position + orbitPos;

        // Oyuncuyu hedef pozisyona doğru çek
        Vector3 direction = targetPos - player.transform.position;
        playerRb.AddForce(direction * playerSuctionForce * Time.deltaTime, ForceMode.Force);

        // Yukarı kaldırma kuvveti
        Vector3 liftDirection = Vector3.up;
        playerRb.AddForce(liftDirection * liftForce * Time.deltaTime, ForceMode.Force);

        // Spin efekti
        playerRb.AddTorque(Vector3.up * orbitSpeed * 100f * Time.deltaTime, ForceMode.Force);

        // 5 saniye sonra fırlat
        if (Time.time - playerCaptureTime >= holdDuration)
        {
            ThrowPlayer();
        }
    }

    void ThrowPlayer()
    {
        // Fırlatma yönü (rastgele ama yukarı ağırlıklı)
        Vector3 throwDirection = new Vector3(
            Random.Range(-1f, 1f),
            Random.Range(1.5f, 2.5f),    // Daha yukarı ağırlıklı
            Random.Range(-1f, 1f)
        ).normalized;

        // Güçlü fırlatma
        Vector3 throwVelocity = throwDirection * (throwForce * 1.5f + currentSize * 150f);

        // Gravity'yi aç ve fırlat
        playerRb.useGravity = true;
        playerRb.linearDamping = 0.5f;
        playerRb.linearVelocity = Vector3.zero; // Önceki hızı sıfırla
        playerRb.AddForce(throwVelocity, ForceMode.Impulse);

        // Random spin
        Vector3 randomTorque = new Vector3(
            Random.Range(-300f, 300f),
            Random.Range(-300f, 300f),
            Random.Range(-300f, 300f)
        );
        playerRb.AddTorque(randomTorque, ForceMode.Impulse);

        // Yakalama durumunu sıfırla
        playerCaught = false;

        if (showDebugInfo)
        {
            Debug.Log("💥 OYUNCU FIRLATLADI! Yön: " + throwDirection + " - Kuvvet: " + throwVelocity.magnitude);
        }
    }

    void ReleasePlayer()
    {
        // Oyuncuyu serbest bırak
        playerCaught = false;
        playerRb.useGravity = true;
        playerRb.linearDamping = 0.5f;

        if (showDebugInfo)
        {
            Debug.Log("🌪️ Oyuncu serbest bırakıldı - Menzil dışında");
        }
    }

    void UpdateCaughtObjects()
    {
        // Çok uzaklaşan nesneleri temizle
        for (int i = caughtObjects.Count - 1; i >= 0; i--)
        {
            if (caughtObjects[i].obj == null)
            {
                caughtObjects.RemoveAt(i);
                continue;
            }

            float distance = Vector3.Distance(transform.position, caughtObjects[i].obj.transform.position);
            if (distance > suctionRange * 2f)
            {
                // Çok uzaklaştı, bırak
                Rigidbody objRb = caughtObjects[i].obj.GetComponent<Rigidbody>();
                if (objRb != null)
                {
                    objRb.useGravity = true;
                    objRb.linearDamping = 0.5f;
                }
                caughtObjects.RemoveAt(i);
            }
        }
    }

    public void GainPower(float amount)
    {
        float oldSize = currentSize;
        currentSize += amount;
        currentSize = Mathf.Clamp(currentSize, minSize, maxSize);

        if (showDebugInfo)
        {
            Debug.Log("⚡ GÜÇ KAZANIMI: " + oldSize.ToString("F1") + " → " + currentSize.ToString("F1"));
        }

        UpdateTornadoSize();
        UpdateParticleSystem();
        UpdateSound();
    }

    public void LosePower(float amount)
    {
        currentSize -= amount;
        currentSize = Mathf.Clamp(currentSize, minSize, maxSize);

        UpdateTornadoSize();
        UpdateParticleSystem();
        UpdateSound();

        if (currentSize <= minSize)
        {
            if (gameManager != null)
            {
                gameManager.GameOver();
            }
        }
    }

    void UpdateTornadoSize()
    {
        float scaleMultiplier = currentSize / 2f;
        transform.localScale = Vector3.one * scaleMultiplier;

        // Çekme alanını güncelle
        suctionRange = 2f + (currentSize * 0.8f);
    }

    void UpdateParticleSystem()
    {
        if (tornadoParticles != null)
        {
            var main = tornadoParticles.main;
            main.startSize = 0.5f + (currentSize * 0.3f);
            main.startSpeed = 5f + (currentSize * 2f);

            var emission = tornadoParticles.emission;
            emission.rateOverTime = 50f + (currentSize * 20f);
        }
    }

    void UpdateSound()
    {
        if (tornadoSound != null)
        {
            tornadoSound.volume = 0.3f + (currentSize / maxSize) * 0.7f;
            tornadoSound.pitch = 0.8f + (currentSize / maxSize) * 0.6f;
        }
    }

    void OnDrawGizmosSelected()
    {
        // Çekme alanı
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, suctionRange);

        // Tornado merkezi
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, 0.5f);

        // Yakalanan nesnelerin orbit path'leri
        Gizmos.color = Color.cyan;
        foreach (var victim in caughtObjects)
        {
            if (victim.obj != null)
            {
                Gizmos.DrawWireSphere(transform.position, victim.orbitRadius);
            }
        }

        // Oyuncu orbit path'i (eğer yakalanmışsa)
        if (playerCaught && player != null)
        {
            Gizmos.color = Color.magenta;
            Gizmos.DrawWireSphere(transform.position, playerOrbitRadius);

            // Oyuncunun mevcut pozisyonu
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(player.transform.position, 0.5f);
        }
    }
}
