// ==========================================
// TORNADO CONTROLLER - GERÇEK FİZİK VERSİYONU
// ==========================================
using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class TornadoController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 360f;

    [Header("Size Settings")]
    public float currentSize = 1f;
    public float maxSize = 10f;
    public float minSize = 0.5f;

    [Header("Tornado Physics")]
    public float suctionRange = 5f;          // Çekme menzili
    public float suctionForce = 500f;        // Çekme kuvveti
    public float liftForce = 300f;           // Yukarı kaldırma
    public float orbitSpeed = 5f;            // Dönüş hızı
    public float throwForce = 1000f;         // Fırlatma kuvveti
    public float holdDuration = 5f;          // Kaç saniye tutacak

    [Header("Detection Settings")]
    public LayerMask destructibleLayer = 1;

    [Header("Effects")]
    public ParticleSystem tornadoParticles;
    public AudioSource tornadoSound;

    [Header("Debug")]
    public bool showDebugInfo = true;

    // Private variables
    private Vector3 movement;
    private Rigidbody rb;
    private GameManager gameManager;
    private Transform tornadoMesh;

    // Tornado'nun içindeki nesneler
    private List<TornadoVictim> caughtObjects = new List<TornadoVictim>();

    void Start()
    {
        rb = GetComponent<Rigidbody>();
        gameManager = FindObjectOfType<GameManager>();

        if (transform.childCount > 0)
        {
            tornadoMesh = transform.GetChild(0);
        }

        UpdateTornadoSize();
        UpdateParticleSystem();

        if (showDebugInfo)
        {
            Debug.Log("🌪️ Tornado başlatıldı - Başlangıç boyutu: " + currentSize);
        }
    }

    void Update()
    {
        HandleInput();
        RotateTornado();
        ApplyTornadoPhysics();
        UpdateCaughtObjects();
    }

    void FixedUpdate()
    {
        MoveTornado();
    }

    void HandleInput()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        movement = new Vector3(horizontal, 0, vertical).normalized;
    }

    void MoveTornado()
    {
        if (movement.magnitude > 0)
        {
            Vector3 velocity = movement * moveSpeed;
            velocity.y = rb.linearVelocity.y;
            rb.linearVelocity = velocity;
        }
        else
        {
            rb.linearVelocity = new Vector3(0, rb.linearVelocity.y, 0);
        }
    }

    void RotateTornado()
    {
        if (tornadoMesh != null)
        {
            tornadoMesh.Rotate(0, rotationSpeed * Time.deltaTime, 0);
        }
    }

    void ApplyTornadoPhysics()
    {
        // Tornado etki alanındaki tüm nesneleri bul
        Collider[] objectsInRange = Physics.OverlapSphere(transform.position, suctionRange, destructibleLayer);

        foreach (Collider obj in objectsInRange)
        {
            DestructibleObject destructible = obj.GetComponent<DestructibleObject>();
            if (destructible == null) continue;

            Rigidbody objRb = obj.GetComponent<Rigidbody>();
            if (objRb == null)
            {
                objRb = obj.gameObject.AddComponent<Rigidbody>();
                objRb.mass = 1f;
            }

            // Tornado'nun içinde mi kontrol et
            TornadoVictim victim = caughtObjects.Find(v => v.obj == obj.gameObject);

            if (currentSize >= destructible.RequiredSize)
            {
                // Yıkılabilir nesne - tornado'ya çek
                if (victim == null)
                {
                    // İlk kez yakalandı
                    StartCatchingObject(obj.gameObject, destructible);
                }
                else
                {
                    // Zaten yakalanmış - tornado içinde döndür
                    SpinObjectInTornado(objRb, victim);
                }
            }
            else
            {
                // Yıkılamaz ama hafif etki
                ApplyWeakSuction(objRb);
            }
        }
    }

    void StartCatchingObject(GameObject obj, DestructibleObject destructible)
    {
        TornadoVictim newVictim = new TornadoVictim
        {
            obj = obj,
            destructible = destructible,
            captureTime = Time.time,
            orbitAngle = Random.Range(0f, 360f),
            orbitRadius = Random.Range(1f, 2f),
            orbitHeight = Random.Range(2f, 4f)
        };

        caughtObjects.Add(newVictim);

        // Nesneyi havaya kaldır
        Rigidbody objRb = obj.GetComponent<Rigidbody>();
        objRb.useGravity = false; // Gravity'yi kapat
        objRb.linearDamping = 5f; // Hava direnci artır

        if (showDebugInfo)
        {
            Debug.Log("🌪️ Nesne yakalandı: " + obj.name);
        }

        // Güç kazan
        GainPower(destructible.PowerValue);
        if (gameManager != null)
        {
            gameManager.AddScore(destructible.ScoreValue);
        }
    }

    void SpinObjectInTornado(Rigidbody objRb, TornadoVictim victim)
    {
        // Orbit hesapla
        victim.orbitAngle += orbitSpeed * currentSize * Time.deltaTime;

        // Hedef pozisyon (tornado'nun etrafında dönüyor)
        Vector3 orbitPos = new Vector3(
            Mathf.Cos(victim.orbitAngle * Mathf.Deg2Rad) * victim.orbitRadius,
            victim.orbitHeight,
            Mathf.Sin(victim.orbitAngle * Mathf.Deg2Rad) * victim.orbitRadius
        );

        Vector3 targetPos = transform.position + orbitPos;

        // Pozisyona doğru kuvvet uygula
        Vector3 direction = (targetPos - objRb.transform.position);
        objRb.AddForce(direction * suctionForce * Time.deltaTime, ForceMode.Force);

        // Spin efekti
        objRb.AddTorque(Vector3.up * orbitSpeed * 50f * Time.deltaTime, ForceMode.Force);

        // Zamanı dolmuş mu kontrol et
        if (Time.time - victim.captureTime >= holdDuration)
        {
            ThrowObject(objRb, victim);
        }
    }

    void ThrowObject(Rigidbody objRb, TornadoVictim victim)
    {
        // Fırlatma yönü (rastgele ama yukarı ağırlıklı)
        Vector3 throwDirection = new Vector3(
            Random.Range(-1f, 1f),
            Random.Range(1f, 2f),    // Yukarı ağırlıklı
            Random.Range(-1f, 1f)
        ).normalized;

        // Fırlatma kuvveti
        Vector3 throwVelocity = throwDirection * (throwForce + currentSize * 100f);

        // Gravity'yi aç ve fırlat
        objRb.useGravity = true;
        objRb.linearDamping = 0.5f;
        objRb.linearVelocity = Vector3.zero; // Önceki hızı sıfırla
        objRb.AddForce(throwVelocity, ForceMode.Impulse);

        // Random spin
        Vector3 randomTorque = new Vector3(
            Random.Range(-500f, 500f),
            Random.Range(-500f, 500f),
            Random.Range(-500f, 500f)
        );
        objRb.AddTorque(randomTorque, ForceMode.Impulse);

        // Nesneyi yok et
        victim.destructible.DestroyObject();

        // Listeden çıkar
        caughtObjects.Remove(victim);

        if (showDebugInfo)
        {
            Debug.Log("💥 Nesne fırlatıldı: " + victim.obj.name + " - Yön: " + throwDirection);
        }
    }

    void ApplyWeakSuction(Rigidbody objRb)
    {
        // Küçük tornado için hafif çekme
        Vector3 directionToTornado = (transform.position - objRb.transform.position).normalized;
        Vector3 weakForce = directionToTornado * (suctionForce * 0.1f) * Time.deltaTime;

        objRb.AddForce(weakForce, ForceMode.Force);
    }

    void UpdateCaughtObjects()
    {
        // Çok uzaklaşan nesneleri temizle
        for (int i = caughtObjects.Count - 1; i >= 0; i--)
        {
            if (caughtObjects[i].obj == null)
            {
                caughtObjects.RemoveAt(i);
                continue;
            }

            float distance = Vector3.Distance(transform.position, caughtObjects[i].obj.transform.position);
            if (distance > suctionRange * 2f)
            {
                // Çok uzaklaştı, bırak
                Rigidbody objRb = caughtObjects[i].obj.GetComponent<Rigidbody>();
                if (objRb != null)
                {
                    objRb.useGravity = true;
                    objRb.linearDamping = 0.5f;
                }
                caughtObjects.RemoveAt(i);
            }
        }
    }

    public void GainPower(float amount)
    {
        float oldSize = currentSize;
        currentSize += amount;
        currentSize = Mathf.Clamp(currentSize, minSize, maxSize);

        if (showDebugInfo)
        {
            Debug.Log("⚡ GÜÇ KAZANIMI: " + oldSize.ToString("F1") + " → " + currentSize.ToString("F1"));
        }

        UpdateTornadoSize();
        UpdateParticleSystem();
        UpdateSound();
    }

    public void LosePower(float amount)
    {
        currentSize -= amount;
        currentSize = Mathf.Clamp(currentSize, minSize, maxSize);

        UpdateTornadoSize();
        UpdateParticleSystem();
        UpdateSound();

        if (currentSize <= minSize)
        {
            if (gameManager != null)
            {
                gameManager.GameOver();
            }
        }
    }

    void UpdateTornadoSize()
    {
        float scaleMultiplier = currentSize / 2f;
        transform.localScale = Vector3.one * scaleMultiplier;

        // Çekme alanını güncelle
        suctionRange = 2f + (currentSize * 0.8f);
    }

    void UpdateParticleSystem()
    {
        if (tornadoParticles != null)
        {
            var main = tornadoParticles.main;
            main.startSize = 0.5f + (currentSize * 0.3f);
            main.startSpeed = 5f + (currentSize * 2f);

            var emission = tornadoParticles.emission;
            emission.rateOverTime = 50f + (currentSize * 20f);
        }
    }

    void UpdateSound()
    {
        if (tornadoSound != null)
        {
            tornadoSound.volume = 0.3f + (currentSize / maxSize) * 0.7f;
            tornadoSound.pitch = 0.8f + (currentSize / maxSize) * 0.6f;
        }
    }

    void OnDrawGizmosSelected()
    {
        // Çekme alanı
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, suctionRange);

        // Tornado merkezi
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, 0.5f);

        // Yakalanan nesnelerin orbit path'leri
        Gizmos.color = Color.cyan;
        foreach (var victim in caughtObjects)
        {
            if (victim.obj != null)
            {
                Gizmos.DrawWireSphere(transform.position, victim.orbitRadius);
            }
        }
    }
}
